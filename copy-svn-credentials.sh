#!/bin/bash
# Copy SVN credentials from mounted host directory to container user directory

# Check if mounted credentials exist
if [ -d "/host-svn-credentials" ]; then
    echo "Copying SVN credentials from host..."

    # Remove existing .subversion directory to avoid permission conflicts
    rm -rf /home/<USER>/.subversion

    # Create .subversion directory
    mkdir -p /home/<USER>/.subversion

    # Copy all credential files
    cp -r /host-svn-credentials/* /home/<USER>/.subversion/

    # Set proper ownership first
    chown -R appuser:appuser /home/<USER>/.subversion

    # Set proper permissions
    chmod 755 /home/<USER>/.subversion
    chmod 644 /home/<USER>/.subversion/config
    chmod 644 /home/<USER>/.subversion/servers
    chmod 644 /home/<USER>/.subversion/README.txt
    chmod 755 /home/<USER>/.subversion/auth

    # Set permissions for auth subdirectories and files
    if [ -d "/home/<USER>/.subversion/auth" ]; then
        find /home/<USER>/.subversion/auth -type d -exec chmod 755 {} \;
        find /home/<USER>/.subversion/auth -type f -exec chmod 600 {} \;
    fi

    echo "SVN credentials copied successfully"
else
    echo "No SVN credentials found at /host-svn-credentials"
fi

# Continue with the original command
exec "$@"
