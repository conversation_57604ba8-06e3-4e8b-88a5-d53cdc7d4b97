# RepoSense AI - Binary Deployment Guide for Linux

## 🚀 Quick Start

### Prerequisites
- Linux server with <PERSON><PERSON> and Docker Compose
- Existing Ollama service running
- RepoSense AI source code

### Validate Prerequisites

Before starting deployment, verify your environment:

```bash
# Check Docker and Docker Compose
docker --version
docker-compose --version

# Verify Ollama is running and accessible
docker ps | grep ollama
curl http://localhost:11434/api/tags

# Check available disk space (minimum 2GB recommended)
df -h

# Verify network connectivity
ping google.com
```

### Step 1: Copy Files to Linux Server

```bash
# From Windows, copy RepoSense AI to your Linux server
scp -r C:\home-repos\reposense_ai user@your-server:~/reposense-ai

# Or clone from git
git clone <your-repo-url> ~/reposense-ai
```

### Step 2: Deploy RepoSense AI

```bash
# Navigate to RepoSense AI directory
cd ~/reposense-ai

# Make deployment script executable
chmod +x deploy-binary-linux.sh

# Run deployment setup
./deploy-binary-linux.sh

# Navigate to deployment directory
cd deployment

# Customize configuration
nano config/config.production.json
```

### Step 3: Build and Start

```bash
# Ensure Ollama network exists (if not already created)
docker network create ollama-network 2>/dev/null || true

# Build the binary container
docker-compose -f docker-compose.repository-monitor.yml build

# Start RepoSense AI
docker-compose -f docker-compose.repository-monitor.yml up -d

# Check logs
docker-compose -f docker-compose.repository-monitor.yml logs -f repository-monitor
```

### Step 4: Verify Deployment

```bash
# Check health
curl http://localhost:5000/health

# Expected response:
{
  "status": "healthy",
  "services": {
    "monitor": "healthy",
    "database": "healthy",
    "ollama": "healthy"
  }
}
```

## 🔗 Integration with Existing Docker Compose

If you want to integrate with your existing docker-compose.yml:

```bash
# Copy integration script to your docker-compose directory
cp integrate-with-existing-compose.sh /path/to/your/docker-compose/
cd /path/to/your/docker-compose/

# Copy RepoSense AI source
cp -r ~/reposense-ai ./

# Run integration
chmod +x integrate-with-existing-compose.sh
./integrate-with-existing-compose.sh

# Build and start
docker-compose build repository-monitor
docker-compose up -d repository-monitor
```

## ⚙️ Configuration

### Essential Configuration (`config.production.json`)

```json
{
  "repositories": [
    {
      "name": "Your Repository",
      "url": "http://your-svn-server/repo",
      "username": "your_username",
      "password": "your_password",
      "enabled": true
    }
  ],
  "ollama": {
    "base_url": "http://ollama:11434",
    "model": "llama3.2:3b"
  },
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "secret_key": "your-production-secret-key"
  }
}
```

### Recommended Ollama Models

```bash
# Pull recommended models for code analysis
docker exec ollama ollama pull llama3.2:3b
docker exec ollama ollama pull qwen2.5-coder:3b
docker exec ollama ollama pull codellama:7b
```

## 🐳 Container Details

### Binary Container Specifications
- **Base Image**: Ubuntu 22.04 (minimal)
- **Binary Size**: ~20MB
- **Container Size**: ~50MB
- **Memory Usage**: 512MB-2GB
- **CPU Usage**: 0.5-2.0 cores

### Volumes
- `reposense_ai_data`: Application data and database
- `reposense_ai_logs`: Application logs

### Ports
- `5000`: Web interface

### Networks
- `ollama-network`: Connects to your existing Ollama service

## 🔧 Troubleshooting

### Build Issues

```bash
# Check build logs
docker-compose -f docker-compose.repository-monitor.yml build --no-cache

# If PyInstaller fails, check dependencies
docker run --rm -it python:3.11-slim bash
pip install pyinstaller
```

### Runtime Issues

```bash
# Check container logs
docker logs repository-monitor

# Check health status
curl http://localhost:5000/health

# Access container shell
docker exec -it repository-monitor bash
```

### Common Problems

**Port 5000 in use:**
```bash
# Change port in docker-compose.yml
ports:
  - "5001:5000"  # Use port 5001 instead
```

**Ollama connection failed:**
```bash
# Verify Ollama is accessible
docker exec repository-monitor curl http://ollama:11434/api/tags
```

**SVN authentication:**
```bash
# Mount SVN credentials
volumes:
  - ~/.subversion:/home/<USER>/.subversion:ro
```

## 📊 Monitoring

### Health Checks
- Built-in health endpoint: `/health`
- Docker health checks configured
- Automatic restart on failure

### Logs
```bash
# View logs
docker-compose logs -f repository-monitor

# Log rotation configured (10MB, 3 files)
```

### Resource Monitoring
```bash
# Check resource usage
docker stats repository-monitor

# View in Portainer (if available)
# http://your-server:9000
```

## 🔒 Security

### Container Security
- Runs as non-root user
- Minimal base image
- Resource limits configured
- Health checks enabled

### Network Security
- Isolated network (ollama-network)
- No external network access required
- Internal service communication only

### Data Security
- Persistent volumes for data
- Configuration mounted read-only
- Credentials stored securely

## 🎯 Performance Optimization

### Binary Optimizations
- Single executable (no Python overhead)
- Optimized PyInstaller build
- Minimal runtime dependencies
- Fast startup time

### Container Optimizations
- Multi-stage build (small runtime image)
- Layer caching
- Resource limits
- Health checks

### Ollama Integration
- Direct network connection
- Model caching
- Fallback models configured
- Connection pooling

## 📈 Scaling

### Horizontal Scaling
```yaml
# Add multiple instances
repository-monitor-1:
  # ... same config
  ports:
    - "5000:5000"

repository-monitor-2:
  # ... same config
  ports:
    - "5001:5000"
```

### Load Balancing
```yaml
# Add nginx load balancer
nginx:
  image: nginx:alpine
  ports:
    - "80:80"
  # Configure upstream servers
```

This binary deployment provides optimal performance, security, and integration with your existing AI/ML Docker environment!
