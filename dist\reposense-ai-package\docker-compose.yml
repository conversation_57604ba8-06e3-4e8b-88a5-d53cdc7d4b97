version: '3.8'

services:
  repository-monitor:
    build: .
    container_name: repository-monitor
    restart: unless-stopped

    ports:
      - "5000:5000"

    volumes:
      - reposense_ai_data:/app/data
      - reposense_ai_logs:/app/logs

    environment:
      - REPOSENSE_AI_CONFIG=/app/data/config.json
      - REPOSENSE_AI_DATA_DIR=/app/data
      - REPOSENSE_AI_LOG_DIR=/app/logs

volumes:
  reposense_ai_data:
  reposense_ai_logs:
